import React, { useState } from 'react';
import { StyleSheet, Text, View, Image, ScrollView, TouchableOpacity } from 'react-native';
import { Check, AlertCircle, ChevronDown, ChevronUp, AlertTriangle, Info, Leaf, Droplets, Sun, Thermometer } from 'lucide-react-native';
import { Colors } from '@/constants/colors';
import { IdentificationResult } from '@/types/plant';
import { Button } from '@/components/ui/Button';
import { Card } from '@/components/ui/Card';

interface EnhancedIdentificationResultViewProps {
  result: IdentificationResult;
  onAddToGarden: () => void;
  onNewScan: () => void;
}

export const EnhancedIdentificationResultView: React.FC<EnhancedIdentificationResultViewProps> = ({
  result,
  onAddToGarden,
  onNewScan,
}) => {
  const { plant, confidence, imageUri, identificationData, diagnosisData } = result;
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({});
  const isHighConfidence = confidence >= 0.9;
  const isDiagnosis = !!diagnosisData;

  const toggleSection = (section: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const renderExpandableSection = (title: string, content: React.ReactNode, sectionKey: string) => (
    <Card style={styles.expandableCard}>
      <TouchableOpacity 
        style={styles.expandableHeader} 
        onPress={() => toggleSection(sectionKey)}
      >
        <Text style={styles.expandableTitle}>{title}</Text>
        {expandedSections[sectionKey] ? 
          <ChevronUp size={20} color={Colors.textMuted} /> : 
          <ChevronDown size={20} color={Colors.textMuted} />
        }
      </TouchableOpacity>
      {expandedSections[sectionKey] && (
        <View style={styles.expandableContent}>
          {content}
        </View>
      )}
    </Card>
  );

  const renderToxicityWarning = () => {
    if (!identificationData?.toxicity) return null;
    
    const toxicityLevel = identificationData.toxicity.level;
    if (toxicityLevel === 'none') return null;

    const getWarningColor = () => {
      switch (toxicityLevel) {
        case 'severe': return '#DC3545';
        case 'moderate': return '#FD7E14';
        case 'mild': return '#FFC107';
        default: return '#6C757D';
      }
    };

    return (
      <Card style={StyleSheet.flatten([styles.warningCard, { borderColor: getWarningColor() }])}>
        <View style={styles.warningHeader}>
          <AlertTriangle size={20} color={getWarningColor()} />
          <Text style={[styles.warningTitle, { color: getWarningColor() }]}>
            Toxicity Warning
          </Text>
        </View>
        <Text style={styles.warningText}>
          {identificationData.toxicity.warning}
        </Text>
      </Card>
    );
  };

  const renderCareIndicators = () => (
    <View style={styles.careIndicators}>
      <View style={styles.careIndicator}>
        <Sun size={20} color={Colors.primary} />
        <Text style={styles.careIndicatorLabel}>Light</Text>
        <Text style={styles.careIndicatorValue}>
          {plant.careInstructions.light.charAt(0).toUpperCase() + plant.careInstructions.light.slice(1)}
        </Text>
      </View>
      <View style={styles.careIndicator}>
        <Droplets size={20} color={Colors.primary} />
        <Text style={styles.careIndicatorLabel}>Water</Text>
        <Text style={styles.careIndicatorValue}>
          {plant.careInstructions.water.charAt(0).toUpperCase() + plant.careInstructions.water.slice(1)}
        </Text>
      </View>
      <View style={styles.careIndicator}>
        <Thermometer size={20} color={Colors.primary} />
        <Text style={styles.careIndicatorLabel}>Temp</Text>
        <Text style={styles.careIndicatorValue}>
          {plant.careInstructions.temperature.min}°-{plant.careInstructions.temperature.max}°C
        </Text>
      </View>
    </View>
  );

  return (
    <ScrollView style={styles.container} testID="enhanced-identification-result">
      {/* Header Image with Confidence Badge */}
      <View style={styles.imageContainer}>
        <Image source={{ uri: imageUri }} style={styles.image} />
        <View style={[styles.confidenceBadge, isHighConfidence ? styles.highConfidence : styles.lowConfidence]}>
          {isHighConfidence ? (
            <Check size={16} color={Colors.background} />
          ) : (
            <AlertCircle size={16} color={Colors.background} />
          )}
          <Text style={styles.confidenceText}>{Math.round(confidence * 100)}% Match</Text>
        </View>
      </View>

      <View style={styles.content}>
        {/* Main Identification */}
        <View style={styles.mainIdentification}>
          <Text style={styles.commonName}>{plant.commonName}</Text>
          <Text style={styles.scientificName}>{plant.scientificName}</Text>
          
          {identificationData?.plantType && (
            <View style={styles.plantTypeContainer}>
              <Leaf size={16} color={Colors.primary} />
              <Text style={styles.plantType}>{identificationData.plantType}</Text>
            </View>
          )}
        </View>

        {/* Toxicity Warning */}
        {renderToxicityWarning()}

        {/* Key Information */}
        <Card style={styles.keyInfoCard}>
          <Text style={styles.sectionTitle}>Key Information</Text>
          <Text style={styles.description}>{plant.description}</Text>
          
          {identificationData?.nativeRegion && (
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Native Region:</Text>
              <Text style={styles.infoValue}>{identificationData.nativeRegion}</Text>
            </View>
          )}
          
          {identificationData?.growthHabit && (
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Growth Habit:</Text>
              <Text style={styles.infoValue}>{identificationData.growthHabit}</Text>
            </View>
          )}
          
          {identificationData?.growthRate && (
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>Growth Rate:</Text>
              <Text style={styles.infoValue}>{identificationData.growthRate}</Text>
            </View>
          )}
        </Card>

        {/* Care Requirements */}
        <Card style={styles.careCard}>
          <Text style={styles.sectionTitle}>Care Requirements</Text>
          {renderCareIndicators()}
          
          <View style={styles.careDetails}>
            <View style={styles.careDetailRow}>
              <Text style={styles.careLabel}>Soil:</Text>
              <Text style={styles.careValue}>{plant.careInstructions.soil}</Text>
            </View>
            <View style={styles.careDetailRow}>
              <Text style={styles.careLabel}>Fertilizer:</Text>
              <Text style={styles.careValue}>{plant.careInstructions.fertilizer}</Text>
            </View>
            <View style={styles.careDetailRow}>
              <Text style={styles.careLabel}>Humidity:</Text>
              <Text style={styles.careValue}>
                {plant.careInstructions.humidity.charAt(0).toUpperCase() + plant.careInstructions.humidity.slice(1)}
              </Text>
            </View>
          </View>
        </Card>

        {/* Diagnosis Section (if available) */}
        {isDiagnosis && diagnosisData && (
          <>
            <Card style={styles.diagnosisCard}>
              <Text style={styles.sectionTitle}>Health Diagnosis</Text>
              <View style={styles.diagnosisHeader}>
                <AlertCircle size={20} color="#DC3545" />
                <Text style={styles.diagnosedProblem}>{diagnosisData.diagnosedProblem}</Text>
              </View>
              
              <View style={styles.severityContainer}>
                <Text style={styles.severityLabel}>Severity:</Text>
                <Text style={[styles.severityValue, { 
                  color: diagnosisData.severity === 'Severe' ? '#DC3545' : 
                        diagnosisData.severity === 'Moderate' ? '#FD7E14' : '#28A745' 
                }]}>
                  {diagnosisData.severity}
                </Text>
              </View>
              
              <Text style={styles.symptomsTitle}>Symptoms Observed:</Text>
              <Text style={styles.symptomsText}>{diagnosisData.symptomsObserved}</Text>
            </Card>

            <Card style={styles.treatmentCard}>
              <Text style={styles.sectionTitle}>Treatment Plan</Text>
              
              <Text style={styles.treatmentSubtitle}>Immediate Actions:</Text>
              {diagnosisData.immediateActions?.map((action: string, index: number) => (
                <Text key={index} style={styles.actionItem}>• {action}</Text>
              ))}
              
              <Text style={styles.treatmentSubtitle}>Long-term Care:</Text>
              {diagnosisData.longTermCare?.map((care: string, index: number) => (
                <Text key={index} style={styles.actionItem}>• {care}</Text>
              ))}
              
              {diagnosisData.prognosis && (
                <>
                  <Text style={styles.treatmentSubtitle}>Prognosis:</Text>
                  <Text style={styles.prognosisText}>{diagnosisData.prognosis}</Text>
                </>
              )}
            </Card>
          </>
        )}

        {/* Additional Information (Expandable) */}
        {identificationData?.additionalInfo && (
          <>
            {renderExpandableSection(
              "Pests & Diseases",
              <Text style={styles.expandableText}>{identificationData.additionalInfo.pestsAndDiseases}</Text>,
              "pests"
            )}
            
            {renderExpandableSection(
              "Fun Facts",
              <Text style={styles.expandableText}>{identificationData.additionalInfo.funFacts}</Text>,
              "facts"
            )}
            
            {renderExpandableSection(
              "Uses & Benefits",
              <View>
                {identificationData.additionalInfo.uses?.map((use: string, index: number) => (
                  <Text key={index} style={styles.expandableText}>• {use}</Text>
                ))}
              </View>,
              "uses"
            )}
            
            {renderExpandableSection(
              "Propagation",
              <Text style={styles.expandableText}>{identificationData.additionalInfo.propagation}</Text>,
              "propagation"
            )}
            
            {renderExpandableSection(
              "Seasonal Care",
              <Text style={styles.expandableText}>{identificationData.additionalInfo.seasonalCare}</Text>,
              "seasonal"
            )}
          </>
        )}

        {/* Action Buttons */}
        <View style={styles.actions}>
          <Button 
            title="Add to My Garden" 
            onPress={onAddToGarden} 
            style={styles.addButton}
            testID="add-to-garden-button"
          />
          <Button 
            title="New Scan" 
            variant="outline" 
            onPress={onNewScan} 
            style={styles.newScanButton}
            testID="new-scan-button"
          />
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  imageContainer: {
    position: 'relative',
  },
  image: {
    width: '100%',
    height: 300,
  },
  confidenceBadge: {
    position: 'absolute',
    bottom: 16,
    right: 16,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  highConfidence: {
    backgroundColor: Colors.primary,
  },
  lowConfidence: {
    backgroundColor: Colors.error,
  },
  confidenceText: {
    color: Colors.background,
    fontWeight: '600',
    fontSize: 14,
    marginLeft: 4,
  },
  content: {
    padding: 20,
  },
  mainIdentification: {
    marginBottom: 16,
  },
  commonName: {
    fontSize: 28,
    fontWeight: '700',
    color: Colors.text,
    marginBottom: 4,
  },
  scientificName: {
    fontSize: 18,
    fontStyle: 'italic',
    color: Colors.textLight,
    marginBottom: 8,
  },
  plantTypeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  plantType: {
    fontSize: 14,
    color: Colors.primary,
    fontWeight: '500',
    marginLeft: 6,
  },
  warningCard: {
    marginBottom: 16,
    backgroundColor: '#FFF3CD',
    borderWidth: 1,
    borderColor: '#FFEAA7',
  },
  warningHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  warningTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  warningText: {
    fontSize: 14,
    color: '#856404',
    lineHeight: 20,
  },
  keyInfoCard: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 12,
  },
  description: {
    fontSize: 16,
    color: Colors.text,
    lineHeight: 24,
    marginBottom: 16,
  },
  infoRow: {
    flexDirection: 'row',
    marginVertical: 4,
  },
  infoLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.text,
    width: 120,
  },
  infoValue: {
    fontSize: 14,
    color: Colors.textLight,
    flex: 1,
  },
  careCard: {
    marginBottom: 16,
  },
  careIndicators: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 16,
  },
  careIndicator: {
    alignItems: 'center',
    flex: 1,
  },
  careIndicatorLabel: {
    fontSize: 12,
    color: Colors.textMuted,
    marginTop: 4,
  },
  careIndicatorValue: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.text,
    marginTop: 2,
  },
  careDetails: {
    marginTop: 8,
  },
  careDetailRow: {
    flexDirection: 'row',
    marginVertical: 4,
  },
  careLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.text,
    width: 100,
  },
  careValue: {
    fontSize: 14,
    color: Colors.textLight,
    flex: 1,
  },
  diagnosisCard: {
    marginBottom: 16,
    backgroundColor: '#FFF3CD',
    borderColor: '#FFEAA7',
  },
  diagnosisHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  diagnosedProblem: {
    fontSize: 18,
    fontWeight: '600',
    color: '#DC3545',
    marginLeft: 8,
    flex: 1,
  },
  severityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  severityLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.text,
    marginRight: 8,
  },
  severityValue: {
    fontSize: 14,
    fontWeight: '600',
  },
  symptomsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 4,
  },
  symptomsText: {
    fontSize: 14,
    color: '#856404',
    lineHeight: 20,
  },
  treatmentCard: {
    marginBottom: 16,
    backgroundColor: '#D1ECF1',
    borderColor: '#BEE5EB',
  },
  treatmentSubtitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#0C5460',
    marginTop: 12,
    marginBottom: 8,
  },
  actionItem: {
    fontSize: 14,
    color: '#0C5460',
    lineHeight: 20,
    marginBottom: 4,
  },
  prognosisText: {
    fontSize: 14,
    color: '#0C5460',
    lineHeight: 20,
    fontStyle: 'italic',
  },
  expandableCard: {
    marginBottom: 12,
  },
  expandableHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 4,
  },
  expandableTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
  },
  expandableContent: {
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },
  expandableText: {
    fontSize: 14,
    color: Colors.textLight,
    lineHeight: 20,
    marginBottom: 4,
  },
  actions: {
    marginTop: 24,
  },
  addButton: {
    marginBottom: 12,
  },
  newScanButton: {
    marginBottom: 24,
  },
});
