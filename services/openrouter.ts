import { API_CONFIG } from '@/config/api';
import { Plant, CareInstructions } from '@/types/plant';

export interface OpenRouterResponse {
  choices: {
    message: {
      content: string;
    };
  }[];
}

export interface PlantIdentificationData {
  scientificName: string;
  commonName: string;
  description: string;
  careInstructions: CareInstructions;
  tags: string[];
  confidence: number;
  plantType: string;
  nativeRegion: string;
  toxicity: {
    level: string;
    warning: string;
  };
  growthHabit: string;
  growthRate: string;
  additionalInfo: {
    pestsAndDiseases: string;
    funFacts: string;
    uses: string[];
    propagation: string;
    seasonalCare: string;
  };
  diagnosis?: string;
  treatment?: string;
}

export interface DiagnosisData extends PlantIdentificationData {
  diagnosedProblem: string;
  likelyCauses: string[];
  symptomsObserved: string;
  severity: string;
  immediateActions: string[];
  longTermCare: string[];
  productRecommendations: string[];
  stepByStepInstructions: string[];
  preventionTips: string[];
  prognosis: string;
}

export class OpenRouterService {
  private static async makeRequest(imageUri: string, prompt: string): Promise<string> {
    try {
      const response = await fetch(`${API_CONFIG.OPENROUTER_BASE_URL}/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${API_CONFIG.OPENROUTER_API_KEY}`,
          'HTTP-Referer': 'https://plantst.com',
          'X-Title': 'Plantst: Plant Identifier',
        },
        body: JSON.stringify({
          model: API_CONFIG.MODEL,
          messages: [
            {
              role: 'user',
              content: [
                {
                  type: 'text',
                  text: prompt,
                },
                {
                  type: 'image_url',
                  image_url: {
                    url: imageUri,
                  },
                },
              ],
            },
          ],
        }),
      });

      if (!response.ok) {
        throw new Error(`OpenRouter API error: ${response.status} ${response.statusText}`);
      }

      const data: OpenRouterResponse = await response.json();
      return data.choices[0]?.message?.content || '';
    } catch (error) {
      console.error('OpenRouter API error:', error);
      throw error;
    }
  }

  static async identifyPlant(imageUri: string): Promise<PlantIdentificationData> {
    const prompt = `Analyze this plant image and provide comprehensive information in the following JSON format:

{
  "scientificName": "Scientific name (Genus species)",
  "commonName": "Most recognizable common name",
  "description": "Detailed description of appearance, size, and key characteristics (3-4 sentences)",
  "plantType": "e.g., Houseplant, Outdoor Plant, Tree, Shrub, Flower, Herb, Succulent, Fern, Vine",
  "nativeRegion": "Where the plant originally comes from",
  "toxicity": {
    "level": "none|mild|moderate|severe",
    "warning": "Clear warning text about toxicity to humans/pets"
  },
  "growthHabit": "e.g., Upright, Bushy, Trailing, Climber, Groundcover",
  "growthRate": "Fast|Moderate|Slow",
  "careInstructions": {
    "light": "low|medium|high",
    "water": "low|medium|high",
    "temperature": {"min": 15, "max": 30, "unit": "C"},
    "humidity": "low|medium|high",
    "soil": "Detailed soil requirements",
    "fertilizer": "Detailed fertilization needs",
    "toxicity": "none|mild|moderate|severe"
  },
  "additionalInfo": {
    "pestsAndDiseases": "Common pests and diseases this plant faces",
    "funFacts": "Interesting facts or cultural meanings",
    "uses": ["Ornamental", "Edible", "Medicinal", "Air Purifier"],
    "propagation": "How to propagate this plant",
    "seasonalCare": "Seasonal care tips and considerations"
  },
  "tags": ["relevant", "descriptive", "tags"],
  "confidence": 0.95
}

IMPORTANT: Return ONLY the JSON object above, no additional text, explanations, or formatting. Start your response with { and end with }.`;

    const response = await this.makeRequest(imageUri, prompt);
    
    try {
      // Try to extract JSON from the response if it's wrapped in text
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      const jsonString = jsonMatch ? jsonMatch[0] : response;
      
      const parsed = JSON.parse(jsonString);
      
      // Validate required fields
      if (!parsed.scientificName || !parsed.commonName || !parsed.careInstructions) {
        throw new Error('Missing required fields in response');
      }
      
      return parsed;
    } catch (error) {
      console.error('Failed to parse OpenRouter response:', error);
      console.error('Raw response:', response);
      
      // Return a fallback response
      return {
        scientificName: 'Unknown species',
        commonName: 'Unknown plant',
        description: 'Unable to identify this plant. Please try again with a clearer image.',
        plantType: 'Unknown',
        nativeRegion: 'Unknown',
        toxicity: {
          level: 'none',
          warning: 'Toxicity information unavailable'
        },
        growthHabit: 'Unknown',
        growthRate: 'Unknown',
        careInstructions: {
          light: 'medium',
          water: 'medium',
          temperature: { min: 18, max: 25, unit: 'C' },
          humidity: 'medium',
          soil: 'Well-draining potting mix',
          fertilizer: 'Monthly during growing season',
          toxicity: 'none'
        },
        additionalInfo: {
          pestsAndDiseases: 'Information unavailable',
          funFacts: 'Unable to provide information for unidentified plant',
          uses: ['Unknown'],
          propagation: 'Information unavailable',
          seasonalCare: 'Information unavailable'
        },
        tags: ['unknown'],
        confidence: 0.1
      };
    }
  }

  static async diagnosePlant(imageUri: string, problemDescription?: string): Promise<DiagnosisData> {
    const problemText = problemDescription ? `The user reports: "${problemDescription}". ` : '';
    
    const prompt = `Analyze this plant image for identification and comprehensive health diagnosis. ${problemText}Provide detailed information in the following JSON format:

{
  "scientificName": "Scientific name (Genus species)",
  "commonName": "Most recognizable common name",
  "description": "Detailed description of the plant (3-4 sentences)",
  "plantType": "e.g., Houseplant, Outdoor Plant, Tree, Shrub, Flower, Herb, Succulent, Fern, Vine",
  "nativeRegion": "Where the plant originally comes from",
  "toxicity": {
    "level": "none|mild|moderate|severe",
    "warning": "Clear warning text about toxicity to humans/pets"
  },
  "growthHabit": "e.g., Upright, Bushy, Trailing, Climber, Groundcover",
  "growthRate": "Fast|Moderate|Slow",
  "careInstructions": {
    "light": "low|medium|high",
    "water": "low|medium|high",
    "temperature": {"min": 15, "max": 30, "unit": "C"},
    "humidity": "low|medium|high",
    "soil": "Detailed soil requirements",
    "fertilizer": "Detailed fertilization needs",
    "toxicity": "none|mild|moderate|severe"
  },
  "additionalInfo": {
    "pestsAndDiseases": "Common pests and diseases this plant faces",
    "funFacts": "Interesting facts or cultural meanings",
    "uses": ["Ornamental", "Edible", "Medicinal", "Air Purifier"],
    "propagation": "How to propagate this plant",
    "seasonalCare": "Seasonal care tips and considerations"
  },
  "tags": ["relevant", "descriptive", "tags"],
  "confidence": 0.95,
  "diagnosedProblem": "e.g., Overwatering, Fungal Infection: Powdery Mildew, Pest Infestation: Spider Mites",
  "likelyCauses": ["Poor drainage", "Too little light", "Contaminated soil"],
  "symptomsObserved": "Description of what the plant is exhibiting",
  "severity": "Mild|Moderate|Severe",
  "immediateActions": ["Stop watering immediately", "Isolate the plant", "Prune affected leaves"],
  "longTermCare": ["Changes to watering schedule", "Adjust light conditions", "Improve humidity"],
  "productRecommendations": ["Apply neem oil solution", "Use fungicide for powdery mildew"],
  "stepByStepInstructions": ["1. Remove affected leaves", "2. Apply treatment", "3. Monitor progress"],
  "preventionTips": ["How to avoid the problem in the future"],
  "prognosis": "Brief outlook on recovery chances"
}

IMPORTANT: Focus on identifying health issues, diseases, pests, or care problems. Provide specific, actionable treatment recommendations. Return ONLY the JSON object above, no additional text, explanations, or formatting. Start your response with { and end with }.`;

    const response = await this.makeRequest(imageUri, prompt);
    
    try {
      // Try to extract JSON from the response if it's wrapped in text
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      const jsonString = jsonMatch ? jsonMatch[0] : response;
      
      const parsed = JSON.parse(jsonString);
      
      // Validate required fields
      if (!parsed.scientificName || !parsed.commonName || !parsed.careInstructions) {
        throw new Error('Missing required fields in response');
      }
      
      return parsed;
    } catch (error) {
      console.error('Failed to parse OpenRouter response:', error);
      console.error('Raw response:', response);
      
      // Return a fallback response
      return {
        scientificName: 'Unknown species',
        commonName: 'Unknown plant',
        description: 'Unable to diagnose this plant. Please try again with a clearer image.',
        plantType: 'Unknown',
        nativeRegion: 'Unknown',
        toxicity: {
          level: 'none',
          warning: 'Toxicity information unavailable'
        },
        growthHabit: 'Unknown',
        growthRate: 'Unknown',
        careInstructions: {
          light: 'medium',
          water: 'medium',
          temperature: { min: 18, max: 25, unit: 'C' },
          humidity: 'medium',
          soil: 'Well-draining potting mix',
          fertilizer: 'Monthly during growing season',
          toxicity: 'none'
        },
        additionalInfo: {
          pestsAndDiseases: 'Information unavailable',
          funFacts: 'Unable to provide information for unidentified plant',
          uses: ['Unknown'],
          propagation: 'Information unavailable',
          seasonalCare: 'Information unavailable'
        },
        tags: ['unknown'],
        confidence: 0.1,
        diagnosedProblem: 'Unable to diagnose from image',
        likelyCauses: ['Image quality too poor for analysis'],
        symptomsObserved: 'Unable to identify symptoms clearly',
        severity: 'Unknown',
        immediateActions: ['Retake photo with better lighting'],
        longTermCare: ['Consult a plant expert'],
        productRecommendations: ['No recommendations available'],
        stepByStepInstructions: ['1. Take a clearer photo', '2. Try again'],
        preventionTips: ['Ensure good image quality for accurate diagnosis'],
        prognosis: 'Unable to assess without clear diagnosis'
      };
    }
  }
}
